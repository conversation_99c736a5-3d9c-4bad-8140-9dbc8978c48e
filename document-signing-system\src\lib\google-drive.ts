import { google } from 'googleapis'

const SCOPES = ['https://www.googleapis.com/auth/drive.file']

class GoogleDriveService {
  private drive: any

  constructor() {
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      'urn:ietf:wg:oauth:2.0:oob'
    )

    auth.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN
    })

    this.drive = google.drive({ version: 'v3', auth })
  }

  async createFolder(name: string, parentId?: string) {
    try {
      const fileMetadata = {
        name: name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: parentId ? [parentId] : undefined
      }

      const response = await this.drive.files.create({
        resource: fileMetadata,
        fields: 'id'
      })

      return response.data.id
    } catch (error) {
      console.error('Error creating folder:', error)
      throw error
    }
  }

  async uploadFile(
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string,
    folderId?: string
  ) {
    try {
      const fileMetadata = {
        name: fileName,
        parents: folderId ? [folderId] : undefined
      }

      const media = {
        mimeType: mimeType,
        body: fileBuffer
      }

      const response = await this.drive.files.create({
        resource: fileMetadata,
        media: media,
        fields: 'id, name, webViewLink, webContentLink'
      })

      return {
        id: response.data.id,
        name: response.data.name,
        webViewLink: response.data.webViewLink,
        webContentLink: response.data.webContentLink
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }

  async downloadFile(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        alt: 'media'
      })

      return response.data
    } catch (error) {
      console.error('Error downloading file:', error)
      throw error
    }
  }

  async getFileInfo(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId: fileId,
        fields: 'id, name, mimeType, size, createdTime, modifiedTime, webViewLink, webContentLink'
      })

      return response.data
    } catch (error) {
      console.error('Error getting file info:', error)
      throw error
    }
  }

  async deleteFile(fileId: string) {
    try {
      await this.drive.files.delete({
        fileId: fileId
      })
      return true
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  }

  async listFiles(folderId?: string) {
    try {
      const query = folderId ? `'${folderId}' in parents` : undefined
      
      const response = await this.drive.files.list({
        q: query,
        fields: 'files(id, name, mimeType, size, createdTime, modifiedTime, webViewLink)'
      })

      return response.data.files
    } catch (error) {
      console.error('Error listing files:', error)
      throw error
    }
  }

  async ensureFoldersExist() {
    try {
      // Check if main folders exist, create if they don't
      const uploadsFolderId = await this.findOrCreateFolder(process.env.UPLOAD_FOLDER || 'uploads')
      const signedFolderId = await this.findOrCreateFolder(process.env.SIGNED_FOLDER || 'signed')
      
      return {
        uploadsFolderId,
        signedFolderId
      }
    } catch (error) {
      console.error('Error ensuring folders exist:', error)
      throw error
    }
  }

  private async findOrCreateFolder(folderName: string) {
    try {
      // Search for existing folder
      const response = await this.drive.files.list({
        q: `name='${folderName}' and mimeType='application/vnd.google-apps.folder'`,
        fields: 'files(id, name)'
      })

      if (response.data.files && response.data.files.length > 0) {
        return response.data.files[0].id
      }

      // Create folder if it doesn't exist
      return await this.createFolder(folderName)
    } catch (error) {
      console.error('Error finding or creating folder:', error)
      throw error
    }
  }
}

export const googleDriveService = new GoogleDriveService()
