// Test script to verify Google APIs are working
const { google } = require('googleapis');

async function testGoogleAPIs() {
  try {
    console.log('Testing Google APIs...');
    
    // Set up authentication
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      'urn:ietf:wg:oauth:2.0:oob'
    );

    auth.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN
    });

    // Test Google Drive API
    console.log('Testing Google Drive API...');
    const drive = google.drive({ version: 'v3', auth });
    const driveResponse = await drive.files.list({
      pageSize: 1,
      fields: 'files(id, name)'
    });
    console.log('✅ Google Drive API working!');
    console.log('Sample files:', driveResponse.data.files);

    // Test Google Sheets API
    console.log('Testing Google Sheets API...');
    const sheets = google.sheets({ version: 'v4', auth });
    
    // You'll need to create a test sheet or use an existing one
    if (process.env.GOOGLE_SHEET_ID) {
      const sheetsResponse = await sheets.spreadsheets.get({
        spreadsheetId: process.env.GOOGLE_SHEET_ID
      });
      console.log('✅ Google Sheets API working!');
      console.log('Sheet title:', sheetsResponse.data.properties.title);
    } else {
      console.log('⚠️  GOOGLE_SHEET_ID not set, skipping Sheets test');
    }

    console.log('🎉 All Google APIs are working correctly!');
    
  } catch (error) {
    console.error('❌ Error testing Google APIs:', error.message);
    
    if (error.message.includes('invalid_grant')) {
      console.log('💡 This usually means the refresh token is invalid or expired.');
      console.log('   Please regenerate the refresh token following the steps above.');
    }
    
    if (error.message.includes('insufficient authentication scopes')) {
      console.log('💡 Make sure you selected the correct scopes when generating the token:');
      console.log('   - https://www.googleapis.com/auth/drive.file');
      console.log('   - https://www.googleapis.com/auth/spreadsheets');
    }
  }
}

// Load environment variables
require('dotenv').config();

// Run the test
testGoogleAPIs();
