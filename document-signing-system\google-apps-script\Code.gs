/**
 * Google Apps Script for Document Signing System
 * This script provides additional automation for the Google Sheets integration
 */

// Configuration
const SHEET_NAME = 'Documents'; // Change this to your sheet name if different
const EMAIL_NOTIFICATION_ENABLED = true; // Set to false to disable email notifications

/**
 * Initialize the spreadsheet with proper headers and formatting
 */
function initializeSpreadsheet() {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Set headers
  const headers = [
    'Document ID',
    'User Name', 
    'User Email',
    'Subject',
    'Description',
    'Urgency',
    'Status',
    'Upload Date',
    'Original File URL',
    'Signed File URL',
    'Google Drive ID'
  ];
  
  // Clear existing content and set headers
  sheet.clear();
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Format headers
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  headerRange.setFontWeight('bold');
  headerRange.setHorizontalAlignment('center');
  
  // Set column widths
  sheet.setColumnWidth(1, 150); // Document ID
  sheet.setColumnWidth(2, 120); // User Name
  sheet.setColumnWidth(3, 180); // User Email
  sheet.setColumnWidth(4, 200); // Subject
  sheet.setColumnWidth(5, 250); // Description
  sheet.setColumnWidth(6, 80);  // Urgency
  sheet.setColumnWidth(7, 100); // Status
  sheet.setColumnWidth(8, 120); // Upload Date
  sheet.setColumnWidth(9, 200); // Original File URL
  sheet.setColumnWidth(10, 200); // Signed File URL
  sheet.setColumnWidth(11, 150); // Google Drive ID
  
  // Freeze header row
  sheet.setFrozenRows(1);
  
  Logger.log('Spreadsheet initialized successfully');
}

/**
 * Triggered when a cell is edited
 * Sends notifications when status changes
 */
function onEdit(e) {
  if (!EMAIL_NOTIFICATION_ENABLED) return;
  
  const sheet = e.source.getActiveSheet();
  const range = e.range;
  
  // Check if the edited cell is in the Status column (column 7)
  if (range.getColumn() === 7 && range.getRow() > 1) {
    const row = range.getRow();
    const newStatus = range.getValue();
    const oldStatus = e.oldValue;
    
    // Only send notification if status actually changed
    if (newStatus !== oldStatus) {
      sendStatusChangeNotification(sheet, row, newStatus, oldStatus);
    }
  }
}

/**
 * Send email notification when document status changes
 */
function sendStatusChangeNotification(sheet, row, newStatus, oldStatus) {
  try {
    const data = sheet.getRange(row, 1, 1, 11).getValues()[0];
    const [documentId, userName, userEmail, subject, description, urgency, status, uploadDate, originalFileUrl, signedFileUrl, googleDriveId] = data;
    
    const emailSubject = `Document Status Update: ${subject}`;
    const emailBody = `
Dear ${userName},

Your document status has been updated:

Document: ${subject}
Previous Status: ${oldStatus || 'N/A'}
New Status: ${newStatus}
Urgency: ${urgency}

${description ? 'Description: ' + description : ''}

${originalFileUrl ? 'Original Document: ' + originalFileUrl : ''}
${signedFileUrl ? 'Signed Document: ' + signedFileUrl : ''}

Upload Date: ${uploadDate}

Best regards,
Document Signing System
    `;
    
    // Send email to user
    if (userEmail) {
      MailApp.sendEmail({
        to: userEmail,
        subject: emailSubject,
        body: emailBody
      });
      
      Logger.log(`Notification sent to ${userEmail} for document ${documentId}`);
    }
    
  } catch (error) {
    Logger.log(`Error sending notification: ${error.toString()}`);
  }
}

/**
 * Generate summary report of all documents
 */
function generateSummaryReport() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) {
    Logger.log('No data to generate report');
    return;
  }
  
  // Skip header row
  const documents = data.slice(1);
  
  // Count by status
  const statusCounts = {};
  const urgencyCounts = {};
  
  documents.forEach(row => {
    const status = row[6] || 'Unknown';
    const urgency = row[5] || 'Unknown';
    
    statusCounts[status] = (statusCounts[status] || 0) + 1;
    urgencyCounts[urgency] = (urgencyCounts[urgency] || 0) + 1;
  });
  
  // Generate report
  let report = 'Document Signing System - Summary Report\n';
  report += '=' .repeat(50) + '\n\n';
  report += `Total Documents: ${documents.length}\n\n`;
  
  report += 'Status Breakdown:\n';
  Object.entries(statusCounts).forEach(([status, count]) => {
    report += `  ${status}: ${count}\n`;
  });
  
  report += '\nUrgency Breakdown:\n';
  Object.entries(urgencyCounts).forEach(([urgency, count]) => {
    report += `  ${urgency}: ${count}\n`;
  });
  
  report += '\nGenerated on: ' + new Date().toLocaleString();
  
  Logger.log(report);
  return report;
}

/**
 * Clean up old completed documents (optional)
 * Moves signed documents older than specified days to archive sheet
 */
function archiveOldDocuments(daysOld = 90) {
  const sheet = SpreadsheetApp.getActiveSheet();
  const data = sheet.getDataRange().getValues();
  
  if (data.length <= 1) return;
  
  const headers = data[0];
  const documents = data.slice(1);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);
  
  // Find documents to archive
  const toArchive = [];
  const toKeep = [headers];
  
  documents.forEach((row, index) => {
    const status = row[6];
    const uploadDate = new Date(row[7]);
    
    if (status === 'SIGNED' && uploadDate < cutoffDate) {
      toArchive.push(row);
    } else {
      toKeep.push(row);
    }
  });
  
  if (toArchive.length === 0) {
    Logger.log('No documents to archive');
    return;
  }
  
  // Create or get archive sheet
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let archiveSheet = spreadsheet.getSheetByName('Archive');
  
  if (!archiveSheet) {
    archiveSheet = spreadsheet.insertSheet('Archive');
    archiveSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    
    // Format archive sheet headers
    const headerRange = archiveSheet.getRange(1, 1, 1, headers.length);
    headerRange.setBackground('#34a853');
    headerRange.setFontColor('white');
    headerRange.setFontWeight('bold');
  }
  
  // Move documents to archive
  const lastRow = archiveSheet.getLastRow();
  archiveSheet.getRange(lastRow + 1, 1, toArchive.length, headers.length).setValues(toArchive);
  
  // Update main sheet with remaining documents
  sheet.clear();
  sheet.getRange(1, 1, toKeep.length, headers.length).setValues(toKeep);
  
  // Reformat main sheet headers
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setBackground('#4285f4');
  headerRange.setFontColor('white');
  headerRange.setFontWeight('bold');
  
  Logger.log(`Archived ${toArchive.length} documents`);
}

/**
 * Set up triggers for automatic functions
 */
function setupTriggers() {
  // Delete existing triggers
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  
  // Create onEdit trigger
  ScriptApp.newTrigger('onEdit')
    .onEdit()
    .create();
  
  // Create daily summary trigger (optional)
  ScriptApp.newTrigger('generateSummaryReport')
    .timeBased()
    .everyDays(1)
    .atHour(9) // 9 AM
    .create();
  
  // Create monthly archive trigger (optional)
  ScriptApp.newTrigger('archiveOldDocuments')
    .timeBased()
    .onMonthDay(1) // First day of each month
    .atHour(2) // 2 AM
    .create();
  
  Logger.log('Triggers set up successfully');
}

/**
 * Remove all triggers
 */
function removeTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => ScriptApp.deleteTrigger(trigger));
  Logger.log('All triggers removed');
}

/**
 * Test function to verify everything is working
 */
function testScript() {
  Logger.log('Testing Google Apps Script...');
  
  const sheet = SpreadsheetApp.getActiveSheet();
  Logger.log(`Active sheet: ${sheet.getName()}`);
  Logger.log(`Sheet has ${sheet.getLastRow()} rows`);
  
  // Test summary report
  const report = generateSummaryReport();
  Logger.log('Summary report generated successfully');
  
  Logger.log('Test completed successfully');
}
