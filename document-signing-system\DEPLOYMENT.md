# Deployment Guide

This guide covers deploying the Document Signing System to Vercel and GitHub.

## Prerequisites

- GitHub account
- Vercel account
- Google Cloud Console project with APIs enabled
- Google Sheets and Google Drive set up

## 1. Prepare for Deployment

### Update Environment Variables
Ensure your `.env` file contains all necessary variables:
```env
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://your-domain.vercel.app"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REFRESH_TOKEN="your-google-refresh-token"
GOOGLE_SHEET_ID="your-google-sheet-id"
UPLOAD_FOLDER="uploads"
SIGNED_FOLDER="signed"
```

### Build and Test Locally
```bash
npm run build
npm start
```

## 2. Deploy to GitHub

### Initialize Git Repository
```bash
git init
git add .
git commit -m "Initial commit: Document Signing System"
```

### Create GitHub Repository
1. Go to [GitHub](https://github.com) and create a new repository
2. Name it `document-signing-system`
3. Don't initialize with README (we already have one)

### Push to GitHub
```bash
git branch -M main
git remote add origin https://github.com/yourusername/document-signing-system.git
git push -u origin main
```

## 3. Deploy to Vercel

### Method 1: Vercel Dashboard
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm install`

### Method 2: Vercel CLI
```bash
npm i -g vercel
vercel login
vercel --prod
```

## 4. Configure Environment Variables in Vercel

In your Vercel project dashboard, go to Settings → Environment Variables and add:

| Name | Value | Environment |
|------|-------|-------------|
| `NEXTAUTH_SECRET` | Your production secret | Production |
| `NEXTAUTH_URL` | https://your-domain.vercel.app | Production |
| `GOOGLE_CLIENT_ID` | Your Google Client ID | All |
| `GOOGLE_CLIENT_SECRET` | Your Google Client Secret | All |
| `GOOGLE_REFRESH_TOKEN` | Your Google Refresh Token | All |
| `GOOGLE_SHEET_ID` | Your Google Sheet ID | All |
| `UPLOAD_FOLDER` | uploads | All |
| `SIGNED_FOLDER` | signed | All |

### Generate Production Secret
```bash
openssl rand -base64 32
```

## 5. Database Configuration for Production

### Option 1: Continue with SQLite (Simple)
Keep `DATABASE_URL="file:./dev.db"` for simple deployments.

### Option 2: PostgreSQL (Recommended for Production)
1. Set up a PostgreSQL database (e.g., Supabase, PlanetScale, or Vercel Postgres)
2. Update `DATABASE_URL` in Vercel environment variables
3. Update `prisma/schema.prisma`:
```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

## 6. Post-Deployment Setup

### Update Google OAuth Settings
1. Go to Google Cloud Console
2. Navigate to APIs & Services → Credentials
3. Edit your OAuth 2.0 Client ID
4. Add your Vercel domain to authorized origins:
   - `https://your-domain.vercel.app`
5. Add callback URL:
   - `https://your-domain.vercel.app/api/auth/callback/credentials`

### Update Google Apps Script
1. Open your Google Apps Script project
2. Update any hardcoded URLs to use your production domain
3. Redeploy the script if necessary

### Test the Deployment
1. Visit your Vercel URL
2. Test user registration and login
3. Test document upload
4. Verify Google Drive integration
5. Check Google Sheets updates
6. Test admin functionality

## 7. Custom Domain (Optional)

### Add Custom Domain in Vercel
1. Go to your project settings in Vercel
2. Navigate to Domains
3. Add your custom domain
4. Follow DNS configuration instructions

### Update Environment Variables
Update `NEXTAUTH_URL` to use your custom domain.

## 8. Monitoring and Maintenance

### Vercel Analytics
Enable Vercel Analytics in your project settings for performance monitoring.

### Error Monitoring
Consider integrating error monitoring services like:
- Sentry
- LogRocket
- Bugsnag

### Database Monitoring
Monitor your database performance and set up alerts for:
- Connection limits
- Query performance
- Storage usage

## 9. Continuous Deployment

### Automatic Deployments
Vercel automatically deploys when you push to your main branch.

### Preview Deployments
Vercel creates preview deployments for pull requests.

### Environment-Specific Deployments
- `main` branch → Production
- `develop` branch → Preview
- Feature branches → Preview

## 10. Troubleshooting

### Common Issues

#### Build Failures
- Check build logs in Vercel dashboard
- Ensure all dependencies are in `package.json`
- Verify TypeScript compilation

#### Environment Variable Issues
- Ensure all required variables are set
- Check variable names match exactly
- Verify sensitive values are correct

#### Database Connection Issues
- Verify DATABASE_URL format
- Check database server accessibility
- Ensure connection limits aren't exceeded

#### Google API Issues
- Verify API keys and tokens
- Check API quotas and limits
- Ensure OAuth settings are correct

### Debug Commands
```bash
# Check build locally
npm run build

# Type checking
npm run type-check

# Database operations
npm run db:generate
npm run db:push
```

## 11. Security Checklist

- [ ] Strong `NEXTAUTH_SECRET` in production
- [ ] Environment variables properly configured
- [ ] Google OAuth settings updated
- [ ] Database access restricted
- [ ] HTTPS enabled (automatic with Vercel)
- [ ] File upload validation in place
- [ ] Rate limiting considered

## 12. Performance Optimization

### Vercel Optimizations
- Enable Edge Functions for API routes
- Use Vercel Image Optimization
- Configure caching headers

### Database Optimizations
- Add database indexes
- Implement connection pooling
- Monitor query performance

### File Storage Optimizations
- Implement file compression
- Use CDN for file delivery
- Set up file cleanup routines

## Support

For deployment issues:
1. Check Vercel build logs
2. Review environment variables
3. Test Google API connections
4. Consult the main README.md
5. Create an issue in the GitHub repository
