"use client"

import { useState, useEffect } from "react"
import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  LogOut, 
  User, 
  Download,
  Eye,
  Upload,
  MessageSquare,
  Filter
} from "lucide-react"

interface Document {
  id: string
  title: string
  subject: string
  description: string
  urgency: string
  status: string
  createdAt: string
  originalFileUrl: string
  signedFileUrl?: string
  googleDriveId: string
  user: {
    id: string
    name: string
    email: string
  }
  feedback: Array<{
    id: string
    message: string
    createdAt: string
    admin: {
      name: string
      email: string
    }
  }>
}

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState("ALL")
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [showSignModal, setShowSignModal] = useState(false)
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }

    if (session.user.role !== "ADMIN") {
      router.push("/dashboard")
      return
    }

    fetchDocuments()
  }, [session, status, router])

  useEffect(() => {
    if (statusFilter === "ALL") {
      setFilteredDocuments(documents)
    } else {
      setFilteredDocuments(documents.filter(doc => doc.status === statusFilter))
    }
  }, [documents, statusFilter])

  const fetchDocuments = async () => {
    try {
      const response = await fetch("/api/documents")
      if (response.ok) {
        const data = await response.json()
        setDocuments(data.documents)
      }
    } catch (error) {
      console.error("Error fetching documents:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Clock className="w-5 h-5 text-yellow-500" />
      case "IN_REVIEW":
        return <AlertCircle className="w-5 h-5 text-blue-500" />
      case "SIGNED":
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case "REJECTED":
        return <XCircle className="w-5 h-5 text-red-500" />
      default:
        return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "URGENT":
        return "bg-red-100 text-red-800"
      case "HIGH":
        return "bg-orange-100 text-orange-800"
      case "NORMAL":
        return "bg-blue-100 text-blue-800"
      case "LOW":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const updateDocumentStatus = async (documentId: string, status: string) => {
    try {
      const response = await fetch(`/api/documents/${documentId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      })

      if (response.ok) {
        fetchDocuments()
      }
    } catch (error) {
      console.error("Error updating document status:", error)
    }
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  const pendingCount = documents.filter(doc => doc.status === "PENDING").length
  const inReviewCount = documents.filter(doc => doc.status === "IN_REVIEW").length
  const signedCount = documents.filter(doc => doc.status === "SIGNED").length

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <FileText className="w-8 h-8 text-indigo-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">Admin Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center text-sm text-gray-700">
                <User className="w-4 h-4 mr-2" />
                {session?.user?.name || session?.user?.email} (Admin)
              </div>
              <button
                onClick={() => signOut()}
                className="flex items-center text-sm text-gray-700 hover:text-gray-900"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-semibold text-gray-900">{pendingCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <AlertCircle className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Review</p>
                <p className="text-2xl font-semibold text-gray-900">{inReviewCount}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Signed</p>
                <p className="text-2xl font-semibold text-gray-900">{signedCount}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filter */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">Documents</h2>
              <div className="flex items-center space-x-4">
                <Filter className="w-4 h-4 text-gray-500" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="ALL">All Status</option>
                  <option value="PENDING">Pending</option>
                  <option value="IN_REVIEW">In Review</option>
                  <option value="SIGNED">Signed</option>
                  <option value="REJECTED">Rejected</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="divide-y divide-gray-200">
            {filteredDocuments.length === 0 ? (
              <div className="px-6 py-12 text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No documents found</p>
              </div>
            ) : (
              filteredDocuments.map((doc) => (
                <div key={doc.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="text-sm font-medium text-gray-900">{doc.subject}</h3>
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUrgencyColor(doc.urgency)}`}>
                          {doc.urgency}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{doc.description}</p>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <span>By: {doc.user.name} ({doc.user.email})</span>
                        <span className="mx-2">•</span>
                        <span>Uploaded: {new Date(doc.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        {getStatusIcon(doc.status)}
                        <span className="ml-2 text-sm text-gray-600">{doc.status}</span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => window.open(doc.originalFileUrl, '_blank')}
                          className="p-2 text-gray-400 hover:text-gray-600"
                          title="View Document"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedDocument(doc)
                            setShowFeedbackModal(true)
                          }}
                          className="p-2 text-gray-400 hover:text-gray-600"
                          title="Add Feedback"
                        >
                          <MessageSquare className="w-4 h-4" />
                        </button>
                        {doc.status !== "SIGNED" && (
                          <button
                            onClick={() => {
                              setSelectedDocument(doc)
                              setShowSignModal(true)
                            }}
                            className="p-2 text-green-400 hover:text-green-600"
                            title="Sign Document"
                          >
                            <Upload className="w-4 h-4" />
                          </button>
                        )}
                        <select
                          value={doc.status}
                          onChange={(e) => updateDocumentStatus(doc.id, e.target.value)}
                          className="text-xs border border-gray-300 rounded px-2 py-1"
                        >
                          <option value="PENDING">Pending</option>
                          <option value="IN_REVIEW">In Review</option>
                          <option value="SIGNED">Signed</option>
                          <option value="REJECTED">Rejected</option>
                          <option value="NEEDS_REVISION">Needs Revision</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  {doc.feedback.length > 0 && (
                    <div className="mt-3 pl-4 border-l-2 border-gray-200">
                      <p className="text-xs text-gray-500 mb-1">Recent Feedback:</p>
                      <p className="text-sm text-gray-700">{doc.feedback[doc.feedback.length - 1].message}</p>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Sign Modal */}
      {showSignModal && selectedDocument && (
        <SignModal
          document={selectedDocument}
          onClose={() => {
            setShowSignModal(false)
            setSelectedDocument(null)
          }}
          onSuccess={() => {
            setShowSignModal(false)
            setSelectedDocument(null)
            fetchDocuments()
          }}
        />
      )}

      {/* Feedback Modal */}
      {showFeedbackModal && selectedDocument && (
        <FeedbackModal
          document={selectedDocument}
          onClose={() => {
            setShowFeedbackModal(false)
            setSelectedDocument(null)
          }}
          onSuccess={() => {
            setShowFeedbackModal(false)
            setSelectedDocument(null)
            fetchDocuments()
          }}
        />
      )}
    </div>
  )
}

// Sign Modal Component
function SignModal({ 
  document, 
  onClose, 
  onSuccess 
}: { 
  document: Document
  onClose: () => void
  onSuccess: () => void 
}) {
  const [signedFile, setSignedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!signedFile) return

    setIsUploading(true)
    setError("")

    try {
      const formData = new FormData()
      formData.append("signedFile", signedFile)

      const response = await fetch(`/api/documents/${document.id}/sign`, {
        method: "POST",
        body: formData,
      })

      if (response.ok) {
        onSuccess()
      } else {
        const data = await response.json()
        setError(data.error || "Upload failed")
      }
    } catch (error) {
      setError("Upload failed")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Signed Document</h3>
        <div className="mb-4 p-3 bg-gray-50 rounded">
          <p className="text-sm text-gray-600">Document: {document.subject}</p>
          <p className="text-sm text-gray-600">User: {document.user.name}</p>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Signed Document File
            </label>
            <input
              type="file"
              onChange={(e) => setSignedFile(e.target.files?.[0] || null)}
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100"
              required
            />
          </div>
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUploading || !signedFile}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isUploading ? "Uploading..." : "Upload Signed Document"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Feedback Modal Component
function FeedbackModal({ 
  document, 
  onClose, 
  onSuccess 
}: { 
  document: Document
  onClose: () => void
  onSuccess: () => void 
}) {
  const [message, setMessage] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!message.trim()) return

    setIsSubmitting(true)
    setError("")

    try {
      const response = await fetch(`/api/documents/${document.id}/feedback`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message: message.trim() }),
      })

      if (response.ok) {
        onSuccess()
      } else {
        const data = await response.json()
        setError(data.error || "Failed to add feedback")
      }
    } catch (error) {
      setError("Failed to add feedback")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Add Feedback</h3>
        <div className="mb-4 p-3 bg-gray-50 rounded">
          <p className="text-sm text-gray-600">Document: {document.subject}</p>
          <p className="text-sm text-gray-600">User: {document.user.name}</p>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Feedback Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              placeholder="Enter your feedback here..."
              required
            />
          </div>
          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !message.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 disabled:opacity-50"
            >
              {isSubmitting ? "Submitting..." : "Add Feedback"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
