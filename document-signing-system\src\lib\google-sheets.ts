import { google } from 'googleapis'

class GoogleSheetsService {
  private sheets: any

  constructor() {
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      'urn:ietf:wg:oauth:2.0:oob'
    )

    auth.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN
    })

    this.sheets = google.sheets({ version: 'v4', auth })
  }

  async initializeSheet() {
    try {
      const spreadsheetId = process.env.GOOGLE_SHEET_ID

      // Check if headers exist
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'A1:K1'
      })

      if (!response.data.values || response.data.values.length === 0) {
        // Create headers
        const headers = [
          'Document ID',
          'User Name',
          'User Email',
          'Subject',
          'Description',
          'Urgency',
          'Status',
          'Upload Date',
          'Original File URL',
          'Signed File URL',
          'Google Drive ID'
        ]

        await this.sheets.spreadsheets.values.update({
          spreadsheetId,
          range: 'A1:K1',
          valueInputOption: 'RAW',
          resource: {
            values: [headers]
          }
        })
      }

      return true
    } catch (error) {
      console.error('Error initializing sheet:', error)
      throw error
    }
  }

  async addDocument(documentData: {
    documentId: string
    userName: string
    userEmail: string
    subject: string
    description: string
    urgency: string
    status: string
    uploadDate: string
    originalFileUrl: string
    signedFileUrl?: string
    googleDriveId: string
  }) {
    try {
      const spreadsheetId = process.env.GOOGLE_SHEET_ID

      const values = [
        documentData.documentId,
        documentData.userName,
        documentData.userEmail,
        documentData.subject,
        documentData.description,
        documentData.urgency,
        documentData.status,
        documentData.uploadDate,
        documentData.originalFileUrl,
        documentData.signedFileUrl || '',
        documentData.googleDriveId
      ]

      const response = await this.sheets.spreadsheets.values.append({
        spreadsheetId,
        range: 'A:K',
        valueInputOption: 'RAW',
        resource: {
          values: [values]
        }
      })

      // Return the row number where the data was inserted
      const range = response.data.updates?.updatedRange
      if (range) {
        const rowMatch = range.match(/A(\d+):K\d+/)
        if (rowMatch) {
          return parseInt(rowMatch[1])
        }
      }

      return null
    } catch (error) {
      console.error('Error adding document to sheet:', error)
      throw error
    }
  }

  async updateDocument(
    rowNumber: number,
    updates: {
      status?: string
      signedFileUrl?: string
    }
  ) {
    try {
      const spreadsheetId = process.env.GOOGLE_SHEET_ID

      // Get current row data
      const currentData = await this.sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `A${rowNumber}:K${rowNumber}`
      })

      if (!currentData.data.values || currentData.data.values.length === 0) {
        throw new Error('Row not found')
      }

      const rowData = currentData.data.values[0]

      // Update specific fields
      if (updates.status) {
        rowData[6] = updates.status // Status column
      }
      if (updates.signedFileUrl) {
        rowData[9] = updates.signedFileUrl // Signed File URL column
      }

      // Update the row
      await this.sheets.spreadsheets.values.update({
        spreadsheetId,
        range: `A${rowNumber}:K${rowNumber}`,
        valueInputOption: 'RAW',
        resource: {
          values: [rowData]
        }
      })

      return true
    } catch (error) {
      console.error('Error updating document in sheet:', error)
      throw error
    }
  }

  async getDocumentByRow(rowNumber: number) {
    try {
      const spreadsheetId = process.env.GOOGLE_SHEET_ID

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `A${rowNumber}:K${rowNumber}`
      })

      if (!response.data.values || response.data.values.length === 0) {
        return null
      }

      const rowData = response.data.values[0]
      
      return {
        documentId: rowData[0],
        userName: rowData[1],
        userEmail: rowData[2],
        subject: rowData[3],
        description: rowData[4],
        urgency: rowData[5],
        status: rowData[6],
        uploadDate: rowData[7],
        originalFileUrl: rowData[8],
        signedFileUrl: rowData[9],
        googleDriveId: rowData[10]
      }
    } catch (error) {
      console.error('Error getting document from sheet:', error)
      throw error
    }
  }

  async getAllDocuments() {
    try {
      const spreadsheetId = process.env.GOOGLE_SHEET_ID

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'A2:K' // Skip header row
      })

      if (!response.data.values) {
        return []
      }

      return response.data.values.map((row: any[], index: number) => ({
        rowNumber: index + 2, // +2 because we start from row 2 and arrays are 0-indexed
        documentId: row[0],
        userName: row[1],
        userEmail: row[2],
        subject: row[3],
        description: row[4],
        urgency: row[5],
        status: row[6],
        uploadDate: row[7],
        originalFileUrl: row[8],
        signedFileUrl: row[9],
        googleDriveId: row[10]
      }))
    } catch (error) {
      console.error('Error getting all documents from sheet:', error)
      throw error
    }
  }
}

export const googleSheetsService = new GoogleSheetsService()
