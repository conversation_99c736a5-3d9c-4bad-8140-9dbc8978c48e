{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "GOOGLE_REFRESH_TOKEN": "@google_refresh_token", "GOOGLE_SHEET_ID": "@google_sheet_id", "UPLOAD_FOLDER": "uploads", "SIGNED_FOLDER": "signed"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}}