import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { googleDriveService } from "@/lib/google-drive"
import { googleSheetsService } from "@/lib/google-sheets"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get("file") as File
    const subject = formData.get("subject") as string
    const description = formData.get("description") as string
    const urgency = formData.get("urgency") as string

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      )
    }

    if (!subject) {
      return NextResponse.json(
        { error: "Subject is required" },
        { status: 400 }
      )
    }

    // Validate file type and size
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: "File type not allowed" },
        { status: 400 }
      )
    }

    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size too large (max 10MB)" },
        { status: 400 }
      )
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Ensure Google Drive folders exist
    const { uploadsFolderId } = await googleDriveService.ensureFoldersExist()

    // Upload to Google Drive
    const driveFile = await googleDriveService.uploadFile(
      file.name,
      buffer,
      file.type,
      uploadsFolderId
    )

    // Create document record in database
    const document = await prisma.document.create({
      data: {
        title: file.name,
        subject,
        description: description || "",
        urgency: urgency as any || "NORMAL",
        originalFileUrl: driveFile.webViewLink || "",
        googleDriveId: driveFile.id || "",
        userId: session.user.id,
        status: "PENDING"
      },
      include: {
        user: true
      }
    })

    // Initialize Google Sheets if needed
    await googleSheetsService.initializeSheet()

    // Add to Google Sheets
    const sheetRow = await googleSheetsService.addDocument({
      documentId: document.id,
      userName: document.user.name || "",
      userEmail: document.user.email,
      subject: document.subject,
      description: document.description || "",
      urgency: document.urgency,
      status: document.status,
      uploadDate: document.createdAt.toISOString(),
      originalFileUrl: document.originalFileUrl,
      googleDriveId: document.googleDriveId || ""
    })

    // Update document with sheet row number
    if (sheetRow) {
      await prisma.document.update({
        where: { id: document.id },
        data: { googleSheetRow: sheetRow }
      })
    }

    return NextResponse.json({
      message: "File uploaded successfully",
      document: {
        id: document.id,
        title: document.title,
        subject: document.subject,
        status: document.status,
        createdAt: document.createdAt
      }
    })

  } catch (error) {
    console.error("Upload error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
