/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m10 8 4 4-4 4", key: "1wy4r4" }]
];
const SquareChevronRight = createLucideIcon("square-chevron-right", __iconNode);

export { __iconNode, SquareChevronRight as default };
//# sourceMappingURL=square-chevron-right.js.map
