# Document Signing System

A comprehensive document signing and management system built with Next.js, featuring user and admin roles, Google Drive integration, and Google Sheets automation.

## 🚀 Features

### User Features
- **Document Upload**: Upload documents with subject, description, and urgency levels
- **Status Tracking**: Real-time tracking of document status (Pending, In Review, Signed, etc.)
- **File Management**: Organized storage with separate folders for uploaded and signed documents
- **Notifications**: Real-time notifications for status updates and feedback
- **Download Signed Documents**: Access to signed documents once completed

### Admin Features
- **Dashboard**: Comprehensive admin dashboard with document statistics
- **Document Review**: View and download original documents
- **Signing Workflow**: Upload signed documents with automatic status updates
- **Feedback System**: Provide feedback to users for document revisions
- **Status Management**: Update document status with real-time notifications
- **Filtering**: Filter documents by status, urgency, and other criteria

### Integration Features
- **Google Drive**: Automatic file storage and organization
- **Google Sheets**: Real-time spreadsheet updates with document tracking
- **Google Apps Script**: Automated email notifications and document archiving
- **Authentication**: Secure role-based access control

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: SQLite (development) / PostgreSQL (production)
- **Authentication**: NextAuth.js with credentials provider
- **File Storage**: Google Drive API
- **Spreadsheet Integration**: Google Sheets API
- **Automation**: Google Apps Script
- **Deployment**: Vercel

## 📋 Prerequisites

- Node.js 18+ installed
- Google Account with access to Google Drive and Google Sheets
- Basic knowledge of Google Cloud Console for API setup

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd document-signing-system
npm install
```

### 2. Environment Setup
Copy `.env.example` to `.env` and update with your values:
```env
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REFRESH_TOKEN="your-google-refresh-token"
GOOGLE_SHEET_ID="your-google-sheet-id"
```

### 3. Database Setup
```bash
npx prisma generate
npx prisma db push
```

### 4. Run Development Server
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to access the application.

## 📖 Detailed Setup

For complete setup instructions including Google Cloud Console configuration, Google Drive setup, and Google Apps Script installation, see [SETUP_INSTRUCTIONS.md](./SETUP_INSTRUCTIONS.md).

## 🏗️ Project Structure

```
document-signing-system/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── api/               # API routes
│   │   ├── admin/             # Admin dashboard
│   │   ├── dashboard/         # User dashboard
│   │   ├── login/             # Authentication pages
│   │   └── register/
│   ├── components/            # React components
│   ├── lib/                   # Utility libraries
│   │   ├── auth.ts           # NextAuth configuration
│   │   ├── prisma.ts         # Database client
│   │   ├── google-drive.ts   # Google Drive service
│   │   └── google-sheets.ts  # Google Sheets service
│   └── types/                # TypeScript type definitions
├── prisma/
│   └── schema.prisma         # Database schema
├── google-apps-script/
│   └── Code.gs              # Google Apps Script automation
└── public/                  # Static assets
```

## 🔧 Configuration

### Database Schema
The application uses Prisma with the following main models:
- **User**: User accounts with role-based access
- **Document**: Document records with metadata and status
- **Feedback**: Admin feedback on documents

### Google Integration
- **Google Drive**: Automatic folder creation and file organization
- **Google Sheets**: Real-time document tracking and status updates
- **Google Apps Script**: Email notifications and automation

## 🚀 Deployment

### Deploy to Vercel
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables
4. Deploy

### Environment Variables for Production
Update the following for production:
- `NEXTAUTH_SECRET`: Generate a secure secret
- `NEXTAUTH_URL`: Your production domain
- `DATABASE_URL`: Production database connection string

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Manual Testing Checklist
- [ ] User registration and login
- [ ] Document upload with metadata
- [ ] Admin dashboard functionality
- [ ] Document signing workflow
- [ ] Google Drive integration
- [ ] Google Sheets updates
- [ ] Email notifications (via Apps Script)

## 📱 Usage

### For Users
1. Register with role "USER"
2. Login and upload documents
3. Add subject, description, and urgency level
4. Track document status in dashboard
5. Download signed documents when ready

### For Admins
1. Register with role "ADMIN"
2. Access admin dashboard
3. Review pending documents
4. Provide feedback or upload signed documents
5. Update document status as needed

## 🔒 Security Features

- Role-based access control
- Secure file upload validation
- Environment variable protection
- CSRF protection via NextAuth.js
- Input sanitization and validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For setup help and troubleshooting, see [SETUP_INSTRUCTIONS.md](./SETUP_INSTRUCTIONS.md) or create an issue in the repository.

## 🔄 Updates and Maintenance

- Regular dependency updates
- Security patches
- Feature enhancements based on user feedback
- Performance optimizations

---

Built with ❤️ using Next.js and modern web technologies.
