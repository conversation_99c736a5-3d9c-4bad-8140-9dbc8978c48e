{"name": "document-signing-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "type-check": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^154.0.0", "lucide-react": "^0.525.0", "multer": "^2.0.2", "next": "15.4.3", "next-auth": "^4.24.11", "prisma": "^6.12.0", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}