{"name": "googlea<PERSON>", "version": "154.0.0", "repository": "googleapis/google-api-nodejs-client", "license": "Apache-2.0", "description": "Google APIs Client Library for Node.js", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "files": ["build/src", "!build/src/**/*.map"], "scripts": {"pretest": "npm run build-test", "prepare": "npm run compile", "test": "c8 mocha build/test", "predocs": "npm run build-tools", "precompile": "<PERSON><PERSON><PERSON> build", "prebuild-test": "<PERSON><PERSON><PERSON> build", "docs": "npm run compile && node build/src/generator/docs", "predocs2": "npm run compile", "docs-extract": "node --max-old-space-size=8192 ./node_modules/@microsoft/api-extractor/bin/api-extractor run --local --verbose", "docs-md": "node --max-old-space-size=8192 ./node_modules/@microsoft/api-documenter/bin/api-documenter markdown --input-folder build/docs --output-folder docs", "docs2": "npm run docs-extract && npm run docs-md", "presystem-test": "npm run build-test", "system-test": "mocha build/system-test", "samples-test": "cd samples && npm install && npm link ../ && pwd && npm test", "lint": "gts check", "compile": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc -p tsconfig.json", "build-test": "cross-env NODE_OPTIONS=--max-old-space-size=8192 tsc -p tsconfig.test.json", "build-tools": "tsc -p tsconfig.tools.json", "clean": "gts clean", "fix": "gts fix", "pregenerate": "npm run build-tools", "generate": "node --max-old-space-size=8192 build/src/generator/generator.js", "docs-test": "echo 🙈 this was taking too long and timing out CI", "presubmit-prs": "npm run compile", "submit-prs": "node --max-old-space-size=8192 build/src/generator/synth.js", "prelint": "cd samples; npm link ../; npm i", "predownload": "npm run build-tools", "download": "node build/src/generator/download.js", "preupdate-disclaimers": "npm run build-tools", "update-disclaimers": "node build/src/generator/disclaimer.js"}, "author": "Google Inc.", "keywords": ["google", "api", "google apis", "client", "client library"], "dependencies": {"google-auth-library": "^10.1.0", "googleapis-common": "^8.0.0"}, "devDependencies": {"@types/execa": "^2.0.2", "@types/mocha": "^10.0.10", "@types/mv": "^2.1.4", "@types/ncp": "^2.0.8", "@types/node": "^22.15.3", "@types/nunjucks": "^3.2.6", "@types/prettier": "^3.0.0", "@types/proxyquire": "^1.3.31", "@types/qs": "^6.9.18", "@types/sinon": "^17.0.4", "@types/tmp": "^0.2.6", "@types/url-template": "^2.0.28", "@types/yargs-parser": "^21.0.3", "c8": "^10.1.3", "codecov": "^3.8.3", "cross-env": "^7.0.3", "execa": "^5.0.0", "gaxios": "^7.1.0", "gts": "^6.0.2", "js-green-licenses": "^4.0.0", "jsdoc": "^4.0.4", "jsdoc-fresh": "^4.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "mocha": "^11.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^14.0.4", "nunjucks": "^3.2.4", "open": "^10.1.2", "p-queue": "^6.0.0", "pdfmake": "^0.2.19", "prettier": "^3.5.3", "proxyquire": "^2.1.3", "rimraf": "^5.0.0", "server-destroy": "^1.0.1", "sinon": "^21.0.0", "tmp": "^0.2.3", "typescript": "^5.8.3", "yargs-parser": "^21.1.1"}}