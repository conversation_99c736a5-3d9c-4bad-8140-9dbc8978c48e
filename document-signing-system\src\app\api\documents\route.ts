import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const isAdmin = session.user.role === "ADMIN"

    let documents

    if (isAdmin) {
      // Ad<PERSON> can see all documents
      documents = await prisma.document.findMany({
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          feedback: {
            include: {
              admin: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })
    } else {
      // Regular users can only see their own documents
      documents = await prisma.document.findMany({
        where: {
          userId: session.user.id
        },
        include: {
          feedback: {
            include: {
              admin: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })
    }

    return NextResponse.json({
      documents: documents.map(doc => ({
        id: doc.id,
        title: doc.title,
        subject: doc.subject,
        description: doc.description,
        urgency: doc.urgency,
        status: doc.status,
        originalFileUrl: doc.originalFileUrl,
        signedFileUrl: doc.signedFileUrl,
        googleDriveId: doc.googleDriveId,
        createdAt: doc.createdAt,
        updatedAt: doc.updatedAt,
        user: isAdmin ? doc.user : undefined,
        feedback: doc.feedback
      }))
    })

  } catch (error) {
    console.error("Error fetching documents:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
