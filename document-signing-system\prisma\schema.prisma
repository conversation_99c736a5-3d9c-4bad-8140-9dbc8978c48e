// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  documents Document[]
  feedback  Feedback[]

  @@map("users")
}

model Document {
  id              String         @id @default(cuid())
  title           String
  subject         String
  description     String?
  urgency         UrgencyLevel   @default(NORMAL)
  status          DocumentStatus @default(PENDING)
  originalFileUrl String
  signedFileUrl   String?
  googleDriveId   String?
  googleSheetRow  Int?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  userId   String
  user     User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  feedback Feedback[]

  @@map("documents")
}

model Feedback {
  id        String   @id @default(cuid())
  message   String
  createdAt DateTime @default(now())

  // Relations
  documentId String
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  adminId    String
  admin      User     @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("feedback")
}

enum UserRole {
  USER
  ADMIN
}

enum DocumentStatus {
  PENDING
  IN_REVIEW
  SIGNED
  REJECTED
  NEEDS_REVISION
}

enum UrgencyLevel {
  LOW
  NORMAL
  HIGH
  URGENT
}
