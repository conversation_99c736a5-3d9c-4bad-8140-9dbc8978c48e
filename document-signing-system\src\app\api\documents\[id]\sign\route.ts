import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { googleDriveService } from "@/lib/google-drive"
import { googleSheetsService } from "@/lib/google-sheets"

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const signedFile = formData.get("signedFile") as File
    const documentId = params.id

    if (!signedFile) {
      return NextResponse.json(
        { error: "No signed file provided" },
        { status: 400 }
      )
    }

    // Validate file type and size
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif'
    ]

    if (!allowedTypes.includes(signedFile.type)) {
      return NextResponse.json(
        { error: "File type not allowed" },
        { status: 400 }
      )
    }

    const maxSize = 10 * 1024 * 1024 // 10MB
    if (signedFile.size > maxSize) {
      return NextResponse.json(
        { error: "File size too large (max 10MB)" },
        { status: 400 }
      )
    }

    // Get document from database
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      include: { user: true }
    })

    if (!document) {
      return NextResponse.json(
        { error: "Document not found" },
        { status: 404 }
      )
    }

    // Convert file to buffer
    const bytes = await signedFile.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Ensure Google Drive folders exist
    const { signedFolderId } = await googleDriveService.ensureFoldersExist()

    // Upload signed file to Google Drive
    const signedFileName = `signed_${signedFile.name}`
    const driveFile = await googleDriveService.uploadFile(
      signedFileName,
      buffer,
      signedFile.type,
      signedFolderId
    )

    // Update document in database
    const updatedDocument = await prisma.document.update({
      where: { id: documentId },
      data: {
        signedFileUrl: driveFile.webViewLink || "",
        status: "SIGNED"
      }
    })

    // Update Google Sheets if row number exists
    if (document.googleSheetRow) {
      try {
        await googleSheetsService.updateDocument(document.googleSheetRow, {
          status: "SIGNED",
          signedFileUrl: driveFile.webViewLink || ""
        })
      } catch (error) {
        console.error("Error updating Google Sheets:", error)
        // Don't fail the request if Google Sheets update fails
      }
    }

    return NextResponse.json({
      message: "Document signed successfully",
      document: {
        id: updatedDocument.id,
        signedFileUrl: updatedDocument.signedFileUrl,
        status: updatedDocument.status,
        updatedAt: updatedDocument.updatedAt
      }
    })

  } catch (error) {
    console.error("Error signing document:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
