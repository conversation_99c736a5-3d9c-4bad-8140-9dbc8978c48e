# Document Signing System - Setup Instructions

## Overview
This is a comprehensive document signing system built with Next.js, featuring user and admin roles, Google Drive integration, and Google Sheets automation.

## Prerequisites
- Node.js 18+ installed
- Google Account with access to Google Drive and Google Sheets
- Basic knowledge of Google Cloud Console (for API setup)

## 1. Project Setup

### Clone and Install Dependencies
```bash
cd document-signing-system
npm install
```

### Environment Variables
Update the `.env` file with your actual values:

```env
# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Google Drive API
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_REFRESH_TOKEN="your-google-refresh-token"

# Google Sheets
GOOGLE_SHEET_ID="your-google-sheet-id"

# App Settings
UPLOAD_FOLDER="uploads"
SIGNED_FOLDER="signed"
```

## 2. Google Cloud Console Setup

### Step 1: Create a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Google Drive API
   - Google Sheets API

### Step 2: Create Credentials
1. Go to "Credentials" in the Google Cloud Console
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Configure the OAuth consent screen if prompted
4. Set application type to "Desktop application"
5. Note down the Client ID and Client Secret

### Step 3: Generate Refresh Token
1. Use the Google OAuth 2.0 Playground: https://developers.google.com/oauthplayground/
2. In the settings (gear icon), check "Use your own OAuth credentials"
3. Enter your Client ID and Client Secret
4. In Step 1, select:
   - `https://www.googleapis.com/auth/drive.file`
   - `https://www.googleapis.com/auth/spreadsheets`
5. Click "Authorize APIs" and complete the OAuth flow
6. In Step 2, click "Exchange authorization code for tokens"
7. Copy the refresh token

## 3. Google Sheets Setup

### Step 1: Create a Google Sheet
1. Go to [Google Sheets](https://sheets.google.com/)
2. Create a new spreadsheet
3. Name it "Document Signing System"
4. Copy the Sheet ID from the URL (the long string between `/d/` and `/edit`)

### Step 2: Set Up Google Apps Script
1. In your Google Sheet, go to Extensions → Apps Script
2. Delete the default code and paste the code from `google-apps-script/Code.gs`
3. Save the project and name it "Document Signing Automation"
4. Run the `initializeSpreadsheet()` function to set up headers
5. Run the `setupTriggers()` function to enable automatic notifications

## 4. Google Drive Setup

### Create Folders
The application will automatically create the following folders in your Google Drive:
- `uploads` - for original documents
- `signed` - for signed documents

You can customize these folder names in the `.env` file.

## 5. Database Setup

### Initialize the Database
```bash
npx prisma generate
npx prisma db push
```

### Create Admin User
Run the development server and register a user with role "ADMIN":
```bash
npm run dev
```

Go to `http://localhost:3000/register` and create an admin account.

## 6. Running the Application

### Development Mode
```bash
npm run dev
```

### Production Build
```bash
npm run build
npm start
```

## 7. Deployment to Vercel

### Step 1: Push to GitHub
1. Create a new GitHub repository
2. Push your code to the repository:
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/yourusername/document-signing-system.git
git push -u origin main
```

### Step 2: Deploy to Vercel
1. Go to [Vercel](https://vercel.com/)
2. Import your GitHub repository
3. Add all environment variables from your `.env` file
4. Deploy the application

### Step 3: Update Environment Variables
After deployment, update the `NEXTAUTH_URL` in your environment variables to your Vercel domain.

## 8. Usage

### For Users:
1. Register an account with role "USER"
2. Login and upload documents with subject, description, and urgency level
3. Track document status and download signed documents when ready

### For Admins:
1. Register an account with role "ADMIN"
2. Login to access the admin dashboard
3. Review pending documents
4. Provide feedback to users
5. Upload signed documents
6. Update document status

## 9. Features

### User Features:
- Document upload with metadata
- Status tracking
- Download signed documents
- View feedback from admins

### Admin Features:
- View all documents with filtering
- Download and view original documents
- Upload signed documents
- Provide feedback to users
- Update document status
- Dashboard with statistics

### Automation Features:
- Automatic Google Drive storage
- Real-time Google Sheets updates
- Email notifications (via Google Apps Script)
- Document archiving (optional)

## 10. Troubleshooting

### Common Issues:

1. **Google API Errors**: Ensure all APIs are enabled and credentials are correct
2. **Database Issues**: Run `npx prisma db push` to sync the database
3. **File Upload Issues**: Check file size limits and allowed file types
4. **Google Sheets Not Updating**: Verify the Sheet ID and API permissions

### Logs:
- Check browser console for client-side errors
- Check server logs for API errors
- Check Google Apps Script logs for automation issues

## 11. Security Considerations

- Change the `NEXTAUTH_SECRET` in production
- Use environment variables for all sensitive data
- Regularly rotate API keys and tokens
- Implement proper file validation
- Consider implementing rate limiting for API endpoints

## 12. Customization

### Adding File Types:
Update the `allowedTypes` array in the upload API routes.

### Changing Email Templates:
Modify the Google Apps Script email templates.

### Adding New Document Statuses:
Update the Prisma schema and regenerate the client.

### Styling:
The application uses Tailwind CSS for styling. Customize the design by modifying the component styles.

## Support

For issues and questions, please check the documentation or create an issue in the GitHub repository.
