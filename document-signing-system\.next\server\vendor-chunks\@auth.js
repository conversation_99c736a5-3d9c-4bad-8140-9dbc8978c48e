"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\nfunction PrismaAdapter(prisma) {\n    const p = prisma;\n    return {\n        // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n        createUser: ({ id, ...data }) => p.user.create(stripUndefined(data)),\n        getUser: (id) => p.user.findUnique({ where: { id } }),\n        getUserByEmail: (email) => p.user.findUnique({ where: { email } }),\n        async getUserByAccount(provider_providerAccountId) {\n            const account = await p.account.findUnique({\n                where: { provider_providerAccountId },\n                include: { user: true },\n            });\n            return account?.user ?? null;\n        },\n        updateUser: ({ id, ...data }) => p.user.update({\n            where: { id },\n            ...stripUndefined(data),\n        }),\n        deleteUser: (id) => p.user.delete({ where: { id } }),\n        linkAccount: (data) => p.account.create({ data }),\n        unlinkAccount: (provider_providerAccountId) => p.account.delete({\n            where: { provider_providerAccountId },\n        }),\n        async getSessionAndUser(sessionToken) {\n            const userAndSession = await p.session.findUnique({\n                where: { sessionToken },\n                include: { user: true },\n            });\n            if (!userAndSession)\n                return null;\n            const { user, ...session } = userAndSession;\n            return { user, session };\n        },\n        createSession: (data) => p.session.create(stripUndefined(data)),\n        updateSession: (data) => p.session.update({\n            where: { sessionToken: data.sessionToken },\n            ...stripUndefined(data),\n        }),\n        deleteSession: (sessionToken) => p.session.delete({ where: { sessionToken } }),\n        async createVerificationToken(data) {\n            const verificationToken = await p.verificationToken.create(stripUndefined(data));\n            if (\"id\" in verificationToken && verificationToken.id)\n                delete verificationToken.id;\n            return verificationToken;\n        },\n        async useVerificationToken(identifier_token) {\n            try {\n                const verificationToken = await p.verificationToken.delete({\n                    where: { identifier_token },\n                });\n                if (\"id\" in verificationToken && verificationToken.id)\n                    delete verificationToken.id;\n                return verificationToken;\n            }\n            catch (error) {\n                // If token already used/deleted, just return null\n                // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n                if (error &&\n                    typeof error === \"object\" &&\n                    \"code\" in error &&\n                    error.code === \"P2025\")\n                    return null;\n                throw error;\n            }\n        },\n        async getAccount(providerAccountId, provider) {\n            return p.account.findFirst({\n                where: { providerAccountId, provider },\n            });\n        },\n        async createAuthenticator(data) {\n            return p.authenticator.create(stripUndefined(data));\n        },\n        async getAuthenticator(credentialID) {\n            return p.authenticator.findUnique({\n                where: { credentialID },\n            });\n        },\n        async listAuthenticatorsByUserId(userId) {\n            return p.authenticator.findMany({\n                where: { userId },\n            });\n        },\n        async updateAuthenticatorCounter(credentialID, counter) {\n            return p.authenticator.update({\n                where: { credentialID },\n                data: { counter },\n            });\n        },\n    };\n}\n/** @see https://www.prisma.io/docs/orm/prisma-client/special-fields-and-types/null-and-undefined */\nfunction stripUndefined(obj) {\n    const data = {};\n    for (const key in obj)\n        if (obj[key] !== undefined)\n            data[key] = obj[key];\n    return { data };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ })

};
;