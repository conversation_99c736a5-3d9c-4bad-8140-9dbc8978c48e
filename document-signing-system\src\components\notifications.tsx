"use client"

import { useState, useEffect, create<PERSON>onte<PERSON><PERSON>, use<PERSON>ontext, ReactNode } from "react"
import { X, CheckCircle, AlertCircle, XCircle, Info } from "lucide-react"

interface Notification {
  id: string
  type: "success" | "error" | "warning" | "info"
  title: string
  message: string
  duration?: number
}

interface NotificationContextType {
  notifications: Notification[]
  addNotification: (notification: Omit<Notification, "id">) => void
  removeNotification: (id: string) => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const addNotification = (notification: Omit<Notification, "id">) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newNotification = { ...notification, id }
    
    setNotifications(prev => [...prev, newNotification])

    // Auto remove after duration (default 5 seconds)
    const duration = notification.duration || 5000
    setTimeout(() => {
      removeNotification(id)
    }, duration)
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  return (
    <NotificationContext.Provider value={{ notifications, addNotification, removeNotification }}>
      {children}
      <NotificationContainer />
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider")
  }
  return context
}

function NotificationContainer() {
  const { notifications, removeNotification } = useNotifications()

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onRemove={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  )
}

function NotificationItem({ 
  notification, 
  onRemove 
}: { 
  notification: Notification
  onRemove: () => void 
}) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Trigger animation
    setTimeout(() => setIsVisible(true), 10)
  }, [])

  const handleRemove = () => {
    setIsVisible(false)
    setTimeout(onRemove, 300) // Wait for animation to complete
  }

  const getIcon = () => {
    switch (notification.type) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case "error":
        return <XCircle className="w-5 h-5 text-red-500" />
      case "warning":
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case "info":
        return <Info className="w-5 h-5 text-blue-500" />
      default:
        return <Info className="w-5 h-5 text-gray-500" />
    }
  }

  const getBackgroundColor = () => {
    switch (notification.type) {
      case "success":
        return "bg-green-50 border-green-200"
      case "error":
        return "bg-red-50 border-red-200"
      case "warning":
        return "bg-yellow-50 border-yellow-200"
      case "info":
        return "bg-blue-50 border-blue-200"
      default:
        return "bg-gray-50 border-gray-200"
    }
  }

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        max-w-sm w-full bg-white border rounded-lg shadow-lg p-4
        ${getBackgroundColor()}
      `}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-900">
            {notification.title}
          </p>
          <p className="text-sm text-gray-600 mt-1">
            {notification.message}
          </p>
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={handleRemove}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

// Utility functions for common notifications
export const showSuccessNotification = (addNotification: NotificationContextType['addNotification']) => ({
  success: (title: string, message: string) => addNotification({ type: "success", title, message }),
  error: (title: string, message: string) => addNotification({ type: "error", title, message }),
  warning: (title: string, message: string) => addNotification({ type: "warning", title, message }),
  info: (title: string, message: string) => addNotification({ type: "info", title, message }),
})

// Document-specific notification helpers
export const documentNotifications = {
  uploadSuccess: (addNotification: NotificationContextType['addNotification']) =>
    addNotification({
      type: "success",
      title: "Document Uploaded",
      message: "Your document has been successfully uploaded and is pending review."
    }),
  
  uploadError: (addNotification: NotificationContextType['addNotification'], error: string) =>
    addNotification({
      type: "error",
      title: "Upload Failed",
      message: error || "Failed to upload document. Please try again."
    }),
  
  statusUpdate: (addNotification: NotificationContextType['addNotification'], status: string) =>
    addNotification({
      type: "info",
      title: "Status Updated",
      message: `Document status has been updated to ${status}.`
    }),
  
  signedDocument: (addNotification: NotificationContextType['addNotification']) =>
    addNotification({
      type: "success",
      title: "Document Signed",
      message: "The signed document has been uploaded successfully."
    }),
  
  feedbackAdded: (addNotification: NotificationContextType['addNotification']) =>
    addNotification({
      type: "info",
      title: "Feedback Added",
      message: "Your feedback has been added to the document."
    }),
  
  urgentDocument: (addNotification: NotificationContextType['addNotification'], subject: string) =>
    addNotification({
      type: "warning",
      title: "Urgent Document",
      message: `Urgent document requires attention: ${subject}`,
      duration: 10000 // Show longer for urgent items
    })
}
