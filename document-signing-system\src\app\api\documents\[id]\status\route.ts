import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { googleSheetsService } from "@/lib/google-sheets"

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { status } = await request.json()
    const documentId = params.id

    if (!status) {
      return NextResponse.json(
        { error: "Status is required" },
        { status: 400 }
      )
    }

    // Update document status in database
    const document = await prisma.document.update({
      where: { id: documentId },
      data: { status },
      include: {
        user: true
      }
    })

    // Update status in Google Sheets if row number exists
    if (document.googleSheetRow) {
      try {
        await googleSheetsService.updateDocument(document.googleSheetRow, {
          status
        })
      } catch (error) {
        console.error("Error updating Google Sheets:", error)
        // Don't fail the request if Google Sheets update fails
      }
    }

    return NextResponse.json({
      message: "Document status updated successfully",
      document: {
        id: document.id,
        status: document.status,
        updatedAt: document.updatedAt
      }
    })

  } catch (error) {
    console.error("Error updating document status:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
